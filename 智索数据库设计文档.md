# 智索APP数据库设计文档

## 1. 数据库概述

本文档描述智索APP的数据库设计，包含各实体表、关系结构以及主要字段定义。智索APP是一个集用户管理、内容展示、热点分析和个性化推荐于一体的应用平台。

## 2. 数据库设计原则

- 遵循第三范式设计，减少数据冗余
- 保证数据完整性与一致性
- 考虑系统扩展性与性能需求
- 确保数据安全与用户隐私保护

## 3. 数据库实体关系图

```
+-------------+      +---------------+      +--------------+
|    用户     |------| 用户收藏/点赞 |------|    内容     |
+-------------+      +---------------+      +--------------+
       |                                          |
       |                                          |
       v                                          v
+-------------+                           +--------------+
| 用户兴趣标签 |                           |    热点话题   |
+-------------+                           +--------------+
                                                |
                                                |
                                                v
                                          +--------------+
                                          |    标签分类   |
                                          +--------------+
```

## 4. 数据库表结构设计

### 4.1 用户表(users)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| user_id | varchar | 32 | PK | 用户唯一标识 |
| phone | varchar | 15 | UNIQUE, NOT NULL | 手机号(登录账号) |
| password_hash | varchar | 128 | NULL | 密码哈希值(可为空表示未设置) |
| password_salt | varchar | 32 | NULL | 密码盐值 |
| nickname | varchar | 50 | NULL | 用户昵称 |
| avatar | varchar | 255 | NULL | 头像URL |
| member_level | tinyint | 4 | DEFAULT 0 | 会员等级(0:普通,1:黄金,2:铂金等) |
| create_time | datetime | - | NOT NULL | 注册时间 |
| update_time | datetime | - | NOT NULL | 更新时间 |
| last_login_time | datetime | - | NULL | 最后登录时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:正常,0:禁用) |

### 4.2 用户令牌表(user_tokens)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| token_id | varchar | 32 | PK | 令牌ID |
| user_id | varchar | 32 | FK, NOT NULL | 关联用户ID |
| token | varchar | 255 | NOT NULL | 令牌值 |
| device_type | varchar | 20 | NOT NULL | 设备类型 |
| expire_time | datetime | - | NOT NULL | 过期时间 |
| create_time | datetime | - | NOT NULL | 创建时间 |
| last_active_time | datetime | - | NOT NULL | 最后活跃时间 |

### 4.3 用户搜索历史表(user_search_history)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| history_id | varchar | 32 | PK | 历史记录ID |
| user_id | varchar | 32 | FK, NOT NULL | 关联用户ID |
| keyword | varchar | 100 | NOT NULL | 搜索关键词 |
| search_time | datetime | - | NOT NULL | 搜索时间 |

### 4.4 文章内容表(articles)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| article_id | varchar | 32 | PK | 文章ID |
| title | varchar | 500 | NOT NULL | 文章标题 |
| description | varchar | 2000 | NULL | 文章描述(内容第一段) |
| content | text | - | NOT NULL | 文章内容 |
| cover_image | varchar | 255 | NULL | 封面图URL |
| source | varchar | 50 | NULL | 内容来源(cctv.com) |
| source_url | varchar | 1000 | NULL | 来源URL |
| icon_url | varchar | 1000 | NULL | 图标URL(https://news.cctv.com/favicon.ico) |
| view_count | int | 11 | DEFAULT 0 | 阅读量 |
| comment_count | int | 11 | DEFAULT 0 | 评论量 |
| like_count | int | 11 | DEFAULT 0 | 点赞量 |
| is_read | tinyint | 1 | DEFAULT 0 | 是否已读(1:是,0:否) |
| is_favorite | tinyint | 1 | DEFAULT 0 | 是否已收藏(1:是,0:否) |
| collect_time | datetime | - | NULL | 收集时间(热点内容) |
| publish_time | datetime | - | NOT NULL | 发布时间 |
| create_time | datetime | - | NOT NULL | 创建时间 |
| update_time | datetime | - | NOT NULL | 更新时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:已发布,0:草稿,-1:已删除) |

### 4.5 热点话题表(hot_topics)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| topic_id | varchar | 32 | PK | 话题ID |
| title | varchar | 500 | NOT NULL | 话题标题 |
| description | varchar | 2000 | NULL | 话题描述 |
| source | varchar | 50 | NULL | 来源平台(baidu/tieba/toutiao/ithome/zhihu等) |
| source_url | varchar | 1000 | NULL | 来源URL(链接) |
| hot_value | varchar | 50 | NULL | 热度值(0.4阅读量+0.6搜索量) |
| view_count | int | 11 | DEFAULT 0 | 阅读量 |
| search_count | int | 11 | DEFAULT 0 | 搜索量 |
| trend | tinyint | 4 | DEFAULT 0 | 趋势(1:上升,0:持平,-1:下降) |
| rank | int | 11 | NULL | 热榜排名(根据热度值排名) |
| is_read | tinyint | 1 | DEFAULT 0 | 是否已读(1:是,0:否) |
| is_favorite | tinyint | 1 | DEFAULT 0 | 是否已收藏(1:是,0:否) |
| collect_time | datetime | - | NULL | 收集时间 |
| update_time | datetime | - | NOT NULL | 更新时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:显示,0:不显示) |

### 4.6 标签分类表(tags)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| tag_id | varchar | 32 | PK | 标签ID |
| tag_name | varchar | 50 | NOT NULL | 标签名称 |
| icon | varchar | 255 | NULL | 标签图标 |
| create_time | datetime | - | NOT NULL | 创建时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:启用,0:禁用) |

### 4.7 内容标签关联表(content_tags)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| id | varchar | 32 | PK | 主键 |
| content_id | varchar | 32 | NOT NULL | 内容ID(文章或热点) |
| content_type | varchar | 20 | NOT NULL | 内容类型(article/topic) |
| tag_id | varchar | 32 | FK, NOT NULL | 标签ID |
| create_time | datetime | - | NOT NULL | 创建时间 |

### 4.8 用户兴趣标签表(user_interests)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| interest_id | varchar | 32 | PK | 兴趣标签ID |
| user_id | varchar | 32 | FK, NOT NULL | 用户ID |
| tag_id | varchar | 32 | FK, NOT NULL | 标签ID |
| weight | float | - | DEFAULT 0 | 权重值 |
| create_time | datetime | - | NOT NULL | 创建时间 |
| update_time | datetime | - | NOT NULL | 更新时间 |

### 4.9 用户收藏表(user_favorites)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| favorite_id | varchar | 32 | PK | 收藏ID |
| user_id | varchar | 32 | FK, NOT NULL | 用户ID |
| content_id | varchar | 32 | NOT NULL | 内容ID |
| content_type | varchar | 20 | NOT NULL | 内容类型(article/topic) |
| create_time | datetime | - | NOT NULL | 收藏时间 |

### 4.10 用户点赞表(user_likes)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| like_id | varchar | 32 | PK | 点赞ID |
| user_id | varchar | 32 | FK, NOT NULL | 用户ID |
| content_id | varchar | 32 | NOT NULL | 内容ID |
| content_type | varchar | 20 | NOT NULL | 内容类型(article/topic/comment) |
| create_time | datetime | - | NOT NULL | 点赞时间 |

### 4.11 评论表(comments)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| comment_id | varchar | 32 | PK | 评论ID |
| content_id | varchar | 32 | NOT NULL | 内容ID |
| content_type | varchar | 20 | NOT NULL | 内容类型(article/topic) |
| user_id | varchar | 32 | FK, NOT NULL | 用户ID |
| content | varchar | 1000 | NOT NULL | 评论内容 |
| parent_id | varchar | 32 | NULL | 父评论ID(回复) |
| like_count | int | 11 | DEFAULT 0 | 点赞数 |
| create_time | datetime | - | NOT NULL | 评论时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:显示,0:隐藏) |

### 4.12 系统通知表(system_notices)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| notice_id | varchar | 32 | PK | 通知ID |
| title | varchar | 100 | NOT NULL | 通知标题 |
| content | text | - | NOT NULL | 通知内容 |
| notice_type | tinyint | 4 | DEFAULT 0 | 通知类型(0:系统通知,1:版本更新,2:活动通知) |
| image_url | varchar | 255 | NULL | 图片URL |
| publish_time | datetime | - | NOT NULL | 发布时间 |
| expire_time | datetime | - | NULL | 过期时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:有效,0:无效) |

### 4.13 用户通知表(user_notices)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| id | varchar | 32 | PK | 主键 |
| user_id | varchar | 32 | FK, NOT NULL | 用户ID |
| notice_id | varchar | 32 | FK, NOT NULL | 通知ID |
| is_read | tinyint | 4 | DEFAULT 0 | 是否已读(1:已读,0:未读) |
| read_time | datetime | - | NULL | 阅读时间 |

### 4.14 用户分析报告表(user_analysis)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| analysis_id | varchar | 32 | PK | 分析ID |
| user_id | varchar | 32 | FK, NOT NULL | 用户ID |
| article_id | varchar | 32 | FK, NOT NULL | 文章ID |
| title | varchar | 100 | NOT NULL | 分析标题 |
| description | varchar | 500 | NULL | 分析描述 |
| content | text | - | NOT NULL | 分析内容 |
| create_time | datetime | - | NOT NULL | 创建时间 |
| update_time | datetime | - | NOT NULL | 更新时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:显示,0:隐藏) |

### 4.15 协议表(agreements)

| 字段名 | 类型 | 长度 | 约束 | 说明 |
|-------|------|------|------|------|
| agreement_id | varchar | 32 | PK | 协议ID |
| title | varchar | 100 | NOT NULL | 协议标题 |
| content | text | - | NOT NULL | 协议内容 |
| type | varchar | 20 | NOT NULL | 协议类型(user/privacy) |
| version | varchar | 20 | NOT NULL | 版本号 |
| publish_time | datetime | - | NOT NULL | 发布时间 |
| status | tinyint | 4 | DEFAULT 1 | 状态(1:生效,0:失效) |

## 5. 索引设计

### 5.1 主要索引

1. users表
   - phone索引(UNIQUE)
   - member_level索引

2. user_search_history表
   - (user_id, keyword)组合索引
   - search_time索引

3. articles表
   - title索引
   - source索引
   - (source, source_id)组合索引
   - is_hot索引
   - collect_time索引
   - publish_time索引
   - (status, publish_time)组合索引

4. hot_topics表
   - title索引
   - source索引
   - (source, source_id)组合索引
   - hot_value索引
   - collect_time索引
   - rank索引(UNIQUE)
   - (status, hot_value)组合索引

5. user_favorites表
   - (user_id, content_type)组合索引
   - create_time索引

6. user_likes表
   - (user_id, content_type)组合索引
   - create_time索引

## 6. 数据安全与维护

### 6.1 数据备份策略

- 每日增量备份
- 每周全量备份
- 异地灾备

### 6.2 数据清理策略

- 热点内容数据保留7天
- 用户搜索历史保留30天
- 系统日志保留90天

### 6.3 安全措施

- 用户密码使用盐值加密存储
- 敏感数据传输加密
- 数据库访问权限控制
- 定期安全审计

## 7. 性能优化策略

1. 对热门数据(如热搜榜)实施缓存机制
2. 分表策略：对评论、历史记录等高频写入表可按用户ID或时间范围进行分表
3. 对大文本字段(如文章内容)可考虑单独存储或使用NoSQL数据库
4. 定期优化数据库，清理无效数据，维护索引
5. 监控数据库性能指标，及时调整配置参数

## 8. 数据迁移与版本管理

1. 版本控制：数据库变更需有明确的版本号和变更记录
2. 向后兼容：新增字段默认应有合理的默认值
3. 数据迁移脚本：重大结构调整需有完善的迁移方案
4. 灰度发布：重要变更先在测试环境验证，再分批次实施到生产环境

## 9. 存储引擎选择

推荐使用InnoDB存储引擎，支持事务、行级锁定和外键约束，适合高并发场景和数据一致性要求较高的应用。

## 10. 扩展建议

1. 考虑引入MongoDB存储非结构化数据(如用户行为日志)
2. 使用Redis缓存热点数据，提升访问速度
3. 随着用户量增长，可实施读写分离和分库分表策略
4. 针对热点话题分析，可考虑使用ElasticSearch构建全文搜索能力

以上数据库设计方案基于当前智索APP的功能需求，随着产品功能的扩展和用户规模的增长，数据库结构可能需要进一步优化和调整。 