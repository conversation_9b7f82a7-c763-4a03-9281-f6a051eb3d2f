# 智索APP接口文档

## 目录

- [1. 文档说明](#1-文档说明)
- [2. 接口规范](#2-接口规范)
- [3. 认证模块](#3-认证模块)
- [4. 用户模块](#4-用户模块)
- [5. 上传模块](#5-上传模块)
- [6. 文章模块](#6-文章模块)
- [7. 热点话题模块](#7-热点话题模块)
- [8. 搜索模块](#8-搜索模块)
- [9. 用户交互模块](#9-用户交互模块)
- [10. AI功能模块](#10-ai功能模块)
- [11. 标签模块](#11-标签模块)
- [12. 系统管理模块](#12-系统管理模块)
- [13. 错误码说明](#13-错误码说明)

## 1. 文档说明

本文档描述智索APP的后端接口设计，基于现有的数据库设计和后端代码实现。接口遵循RESTful设计原则，采用HTTP协议进行通信。

### 1.1 接口版本

当前接口版本: v1

### 1.2 接口基础路径

所有接口均以 `/v1` 作为基础路径前缀

### 1.3 认证说明

部分接口需要用户认证，部分接口（如AI聊天、AI分析）可以匿名访问。具体认证要求见各接口说明。

## 2. 接口规范

### 2.1 请求格式

- GET请求：参数通过URL Query Parameters传递
- POST/PUT/DELETE请求：参数通过JSON格式在Request Body中传递
- 编码格式：UTF-8
- Content-Type: application/json

### 2.2 响应格式

统一的JSON响应结构：

```json
{
    "code": 0,            // 状态码，0表示成功，非0表示失败
    "message": "success", // 状态描述
    "data": {             // 业务数据
        // 具体业务数据
    }
}
```

### 2.3 认证方式

采用JWT (JSON Web Token)进行身份认证。

- 登录成功后，服务器返回token
- 客户端请求需在Header中携带token: `Authorization: Bearer {token}`
- token过期或无效时，服务端返回401状态码

### 2.4 分页格式

分页接口统一返回格式：

```json
{
    "content": [],        // 数据列表
    "totalElements": 100, // 总记录数
    "totalPages": 5,      // 总页数
    "size": 20,           // 每页大小
    "number": 0           // 当前页码（从0开始）
}
```

## 3. 认证模块

### 3.1 发送短信验证码

- **接口URL**: `/v1/auth/sms/send`
- **请求方式**: POST
- **功能说明**: 发送短信验证码（测试环境下验证码固定为6666）
- **认证要求**: 无需认证
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| phone | String | 是 | 手机号（格式：1[3-9]xxxxxxxxx） |
| type | String | 是 | 验证码类型（login/register） |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| expireTime | Integer | 验证码有效期(秒) |

- **请求示例**:
```json
{
    "phone": "13800138000",
    "type": "login"
}
```

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "expireTime": 300
    }
}
```

### 3.2 短信验证码登录

- **接口URL**: `/v1/auth/login/sms`
- **请求方式**: POST
- **功能说明**: 使用短信验证码登录（如果用户不存在将自动注册）
- **认证要求**: 无需认证
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| phone | String | 是 | 手机号（格式：1[3-9]xxxxxxxxx） |
| code | String | 是 | 短信验证码 |
| deviceId | String | 是 | 设备ID |
| deviceType | String | 是 | 设备类型(ios/android/h5) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| token | String | JWT令牌 |
| refreshToken | String | 刷新令牌 |
| expiresIn | Long | 令牌有效期(秒) |
| isNewUser | Boolean | 是否新用户 |
| needSetPassword | Boolean | 是否需要设置密码 |
| userInfo | Object | 用户基本信息 |

userInfo对象结构：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| userId | String | 用户ID |
| phone | String | 手机号 |
| nickname | String | 用户昵称 |
| avatar | String | 头像URL |
| memberLevel | Integer | 会员等级 |

- **请求示例**:
```json
{
    "phone": "13800138000",
    "code": "6666",
    "deviceId": "device_id_123456",
    "deviceType": "android"
}
```

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 86400,
        "isNewUser": false,
        "needSetPassword": true,
        "userInfo": {
            "userId": "USR00000000000000000000000000001",
            "phone": "13800138000",
            "nickname": "用户1234",
            "avatar": "https://cdn.zhisuo.com/avatar/default.png",
            "memberLevel": 0
        }
    }
}
```

### 3.3 密码登录

- **接口URL**: `/v1/auth/login/password`
- **请求方式**: POST
- **功能说明**: 使用账号密码登录
- **认证要求**: 无需认证
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| phone | String | 是 | 手机号（格式：1[3-9]xxxxxxxxx） |
| password | String | 是 | 密码 |
| deviceId | String | 是 | 设备ID |
| deviceType | String | 是 | 设备类型(ios/android/h5) |

- **响应参数**:

与3.2短信验证码登录接口相同

- **请求示例**:
```json
{
    "phone": "13800138000",
    "password": "password123",
    "deviceId": "device_id_123456",
    "deviceType": "android"
}
```

### 3.4 设置密码

- **接口URL**: `/v1/auth/password/set`
- **请求方式**: POST
- **功能说明**: 设置用户密码
- **认证要求**: 无需认证
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| userId | String | 是 | 用户ID |
| password | String | 否 | 密码（6-20位，不传则使用默认密码） |
| deviceId | String | 否 | 设备ID |
| deviceType | String | 否 | 设备类型(ios/android/h5) |

- **响应参数**:

与登录接口相同，返回token等信息

### 3.5 跳过密码设置

- **接口URL**: `/v1/auth/password/skip`
- **请求方式**: POST
- **功能说明**: 跳过密码设置，使用默认密码
- **认证要求**: 无需认证
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| userId | String | 是 | 用户ID |
| deviceId | String | 否 | 设备ID |
| deviceType | String | 否 | 设备类型(ios/android/h5) |

- **响应参数**:

与登录接口相同，返回token等信息

### 3.6 刷新Token

- **接口URL**: `/v1/auth/token/refresh`
- **请求方式**: POST
- **功能说明**: 使用refreshToken刷新访问令牌
- **认证要求**: 无需认证
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| refreshToken | String | 是 | 刷新令牌 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| token | String | 新的访问令牌 |
| refreshToken | String | 新的刷新令牌 |
| expiresIn | Long | 令牌有效期(秒) |

## 4. 用户模块

### 4.1 获取用户信息

- **接口URL**: `/v1/user/info`
- **请求方式**: GET
- **功能说明**: 获取当前登录用户信息
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| userId | String | 用户ID |
| phone | String | 手机号 |
| nickname | String | 用户昵称 |
| avatar | String | 头像URL |
| memberLevel | Integer | 会员等级(0:普通,1:黄金,2:铂金等) |

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "userId": "USR00000000000000000000000000001",
        "phone": "13800138000",
        "nickname": "张小明",
        "avatar": "https://cdn.zhisuo.com/avatar/user1.jpg",
        "memberLevel": 0
    }
}
```

### 4.2 更新用户信息

- **接口URL**: `/v1/user/info`
- **请求方式**: PUT
- **功能说明**: 更新用户基本信息
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| nickname | String | 否 | 用户昵称(最大12个字符) |
| avatar | String | 否 | 头像URL |

- **响应参数**:

更新后的用户信息，格式同4.1接口

### 4.3 修改密码

- **接口URL**: `/v1/user/password`
- **请求方式**: PUT
- **功能说明**: 修改用户密码
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| oldPassword | String | 是 | 原密码 |
| newPassword | String | 是 | 新密码(6-20位) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| success | Boolean | 是否成功 |

### 4.4 获取用户最近分析

- **接口URL**: `/v1/user/analysis`
- **请求方式**: GET
- **功能说明**: 获取用户最近的AI分析记录
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认10 |

- **响应参数**:

分页格式，content包含分析记录列表

## 5. 上传模块

### 5.1 上传头像

- **接口URL**: `/v1/upload/avatar`
- **请求方式**: POST
- **功能说明**: 上传用户头像
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **Content-Type**: `multipart/form-data`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| file | File | 是 | 头像文件(图片格式，大小不超过2MB) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| url | String | 上传后的头像URL |

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "url": "https://cdn.zhisuo.com/avatar/user1.jpg"
    }
}
```

## 6. 文章模块

### 6.1 获取文章列表

- **接口URL**: `/v1/articles`
- **请求方式**: GET
- **功能说明**: 获取文章列表
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| source | String | 否 | 来源筛选 |
| sortBy | String | 否 | 排序字段，默认publishTime |
| sortOrder | String | 否 | 排序方向，默认desc |

- **响应参数**:

分页格式，content包含文章列表，每个文章对象包含标签信息

### 6.2 获取文章详情

- **接口URL**: `/v1/articles/{articleId}`
- **请求方式**: GET
- **功能说明**: 获取文章详情内容
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| articleId | String | 是 | 文章ID(路径参数) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| articleId | String | 文章ID |
| title | String | 文章标题 |
| description | String | 文章描述 |
| content | String | 文章内容 |
| coverImage | String | 封面图URL |
| source | String | 来源 |
| sourceUrl | String | 来源URL |
| iconUrl | String | 图标URL |
| viewCount | Integer | 阅读数 |
| commentCount | Integer | 评论数 |
| likeCount | Integer | 点赞数 |
| isLiked | Boolean | 是否已点赞 |
| isFavorited | Boolean | 是否已收藏 |
| publishTime | String | 发布时间 |
| tags | Array | 标签列表 |

### 6.3 切换文章点赞状态

- **接口URL**: `/v1/articles/{articleId}/like`
- **请求方式**: POST
- **功能说明**: 切换文章点赞状态（已点赞则取消，未点赞则点赞）
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| articleId | String | 是 | 文章ID(路径参数) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| isLiked | Boolean | 点赞状态 |
| message | String | 操作结果消息 |

### 6.4 更新文章浏览量

- **接口URL**: `/v1/articles/{articleId}/view`
- **请求方式**: POST
- **功能说明**: 更新文章浏览量
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| articleId | String | 是 | 文章ID(路径参数) |

- **响应参数**: 无

### 6.5 搜索文章

- **接口URL**: `/v1/articles/search`
- **请求方式**: GET
- **功能说明**: 搜索文章
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| keyword | String | 是 | 搜索关键词 |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| sortBy | String | 否 | 排序字段，默认publishTime |
| sortOrder | String | 否 | 排序方向，默认desc |
| source | String | 否 | 来源筛选 |

- **响应参数**:

分页格式，包含搜索关键词信息

### 6.6 手动刷新央视新闻

- **接口URL**: `/v1/articles/refresh-cctv`
- **请求方式**: POST
- **功能说明**: 手动触发央视新闻更新
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

## 7. 热点话题模块

### 7.1 获取热点话题列表

- **接口URL**: `/v1/hot-topics`
- **请求方式**: GET
- **功能说明**: 获取热点话题列表
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| source | String | 否 | 来源平台筛选 |

- **响应参数**:

分页格式，content包含热点话题列表，每个话题对象包含标签和收藏状态信息

### 7.2 获取热点话题详情

- **接口URL**: `/v1/hot-topics/{topicId}`
- **请求方式**: GET
- **功能说明**: 获取热点话题详情
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| topicId | String | 是 | 话题ID(路径参数) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| topicId | String | 话题ID |
| title | String | 话题标题 |
| description | String | 话题描述 |
| source | String | 来源平台 |
| sourceUrl | String | 来源URL |
| hotValue | String | 热度值 |
| viewCount | Integer | 阅读量 |
| searchCount | Integer | 搜索量 |
| trend | Integer | 趋势(1:上升,0:持平,-1:下降) |
| rank | Integer | 热榜排名 |
| isFavorited | Boolean | 是否已收藏 |
| collectTime | String | 收集时间 |
| tags | Array | 标签列表 |

### 7.3 更新热点话题浏览量

- **接口URL**: `/v1/hot-topics/{topicId}/view`
- **请求方式**: POST
- **功能说明**: 更新热点话题浏览量
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| topicId | String | 是 | 话题ID(路径参数) |

- **响应参数**: 无

### 7.4 记录热点话题搜索行为

- **接口URL**: `/v1/hot-topics/{topicId}/search`
- **请求方式**: POST
- **功能说明**: 记录热点话题搜索行为
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| topicId | String | 是 | 话题ID(路径参数) |

- **响应参数**: 无

### 7.5 获取热点话题标签

- **接口URL**: `/v1/hot-topics/{topicId}/tags`
- **请求方式**: GET
- **功能说明**: 获取热点话题的标签列表
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| topicId | String | 是 | 话题ID(路径参数) |

- **响应参数**:

标签列表数组

### 7.6 手动刷新热点话题

- **接口URL**: `/v1/hot-topics/refresh`
- **请求方式**: POST
- **功能说明**: 手动触发热点话题更新
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.7 手动刷新昨日热点话题

- **接口URL**: `/v1/hot-topics/refresh-yesterday`
- **请求方式**: POST
- **功能说明**: 手动触发昨日热点话题更新
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.8 刷新热点话题排名

- **接口URL**: `/v1/hot-topics/refresh-ranks`
- **请求方式**: POST
- **功能说明**: 手动刷新热点话题排名
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.9 刷新热点话题趋势

- **接口URL**: `/v1/hot-topics/refresh-trends`
- **请求方式**: POST
- **功能说明**: 手动刷新热点话题趋势
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.10 刷新昨日热点话题趋势

- **接口URL**: `/v1/hot-topics/refresh-yesterday-trends`
- **请求方式**: POST
- **功能说明**: 手动刷新昨日热点话题趋势
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

## 8. 搜索模块

### 8.1 综合搜索

- **接口URL**: `/v1/search`
- **请求方式**: GET
- **功能说明**: 综合搜索（热点话题+文章）
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| keyword | String | 是 | 搜索关键词 |
| type | String | 否 | 内容类型（topic/article/all），默认all |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| keyword | String | 搜索关键词 |
| type | String | 搜索类型 |
| totalCount | Long | 总结果数 |
| topics | Object | 热点话题搜索结果（分页格式） |
| articles | Object | 文章搜索结果（分页格式） |
| relatedKeywords | Array | 相关搜索建议 |
| stats | Object | 搜索统计信息 |

stats对象结构：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| topicCount | Long | 热点话题数量 |
| articleCount | Long | 文章数量 |
| searchTime | Long | 搜索耗时（毫秒） |
| hasMore | Boolean | 是否有更多结果 |

### 8.2 按标签搜索

- **接口URL**: `/v1/search/tag/{tagId}`
- **请求方式**: GET
- **功能说明**: 按标签搜索内容
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| tagId | String | 是 | 标签ID（路径参数） |
| type | String | 否 | 内容类型（topic/article/all），默认all |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

与8.1综合搜索接口相同

### 8.3 搜索热点话题

- **接口URL**: `/v1/search/topics`
- **请求方式**: GET
- **功能说明**: 搜索热点话题
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| keyword | String | 是 | 搜索关键词 |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| source | String | 否 | 来源筛选 |
| tagId | String | 否 | 标签筛选 |

- **响应参数**:

分页格式的热点话题列表

### 8.4 搜索文章

- **接口URL**: `/v1/search/articles`
- **请求方式**: GET
- **功能说明**: 搜索文章
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| keyword | String | 是 | 搜索关键词 |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| source | String | 否 | 来源筛选 |
| tagId | String | 否 | 标签筛选 |

- **响应参数**:

分页格式的文章列表

### 8.5 搜索建议

- **接口URL**: `/v1/search/suggestions`
- **请求方式**: GET
- **功能说明**: 获取搜索建议
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| keyword | String | 是 | 搜索关键词 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| keyword | String | 搜索关键词 |
| suggestions | Array | 关键词联想建议 |
| hotKeywords | Array | 热门搜索词 |
| relatedTags | Array | 相关标签 |
| historyKeywords | Array | 历史搜索（如果用户已登录） |
| topicSuggestions | Array | 热点话题建议 |

## 9. 用户交互模块

### 9.1 切换收藏状态

- **接口URL**: `/v1/favorites`
- **请求方式**: POST
- **功能说明**: 切换收藏状态（已收藏则取消，未收藏则收藏）
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型（article/topic） |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| isFavorited | Boolean | 收藏状态 |
| message | String | 操作结果消息 |

### 9.2 检查收藏状态

- **接口URL**: `/v1/favorites/check`
- **请求方式**: GET
- **功能说明**: 检查用户是否已收藏
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型（article/topic） |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| isFavorited | Boolean | 收藏状态 |

### 9.3 获取用户收藏列表

- **接口URL**: `/v1/favorites/my`
- **请求方式**: GET
- **功能说明**: 获取用户收藏的内容列表
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentType | String | 否 | 内容类型（article/topic），不传则获取全部 |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

分页格式的收藏列表

### 9.4 切换点赞状态

- **接口URL**: `/v1/likes`
- **请求方式**: POST
- **功能说明**: 切换点赞状态（已点赞则取消，未点赞则点赞）
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型（article/topic/comment） |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| isLiked | Boolean | 点赞状态 |
| message | String | 操作结果消息 |

### 9.5 检查点赞状态

- **接口URL**: `/v1/likes/check`
- **请求方式**: GET
- **功能说明**: 检查用户是否已点赞
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型（article/topic/comment） |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| isLiked | Boolean | 点赞状态 |

### 9.6 获取点赞数量

- **接口URL**: `/v1/likes/count`
- **请求方式**: GET
- **功能说明**: 获取内容的点赞数量
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型（article/topic/comment） |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| likeCount | Long | 点赞数量 |

## 10. AI功能模块

### 10.1 AI聊天

- **接口URL**: `/v1/ai/chat`
- **请求方式**: POST
- **功能说明**: AI智能聊天对话
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| message | String | 是 | 用户消息 |
| sessionId | String | 否 | 会话ID，用于保持对话上下文 |
| model | String | 否 | AI模型，默认使用系统配置 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| sessionId | String | 会话ID |
| message | String | AI回复消息 |
| timestamp | String | 回复时间 |
| model | String | 使用的AI模型 |
| usage | Object | 使用统计信息 |

usage对象结构：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| promptTokens | Integer | 输入token数 |
| completionTokens | Integer | 输出token数 |
| totalTokens | Integer | 总token数 |

### 10.2 AI内容分析

- **接口URL**: `/v1/ai/analyze`
- **请求方式**: POST
- **功能说明**: AI分析内容（文章或热点话题）
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型（article/topic） |
| analysisType | String | 否 | 分析类型（summary/sentiment/keywords），默认summary |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| contentId | String | 内容ID |
| contentType | String | 内容类型 |
| analysisType | String | 分析类型 |
| result | Object | 分析结果 |
| timestamp | String | 分析时间 |

result对象结构（根据analysisType不同而不同）：

**summary类型**：
| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| summary | String | 内容摘要 |
| keyPoints | Array | 关键要点 |
| mainTheme | String | 主要主题 |

**sentiment类型**：
| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| sentiment | String | 情感倾向（positive/negative/neutral） |
| confidence | Float | 置信度（0-1） |
| emotions | Array | 具体情感标签 |

**keywords类型**：
| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| keywords | Array | 关键词列表 |
| entities | Array | 实体识别结果 |
| topics | Array | 主题标签 |

### 10.3 AI健康检查

- **接口URL**: `/v1/ai/health`
- **请求方式**: GET
- **功能说明**: 检查AI服务状态
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| status | String | 服务状态（healthy/unhealthy） |
| models | Array | 可用模型列表 |
| responseTime | Long | 响应时间（毫秒） |
| lastCheck | String | 最后检查时间 |

### 10.4 获取AI分析历史

- **接口URL**: `/v1/ai/analysis/history`
- **请求方式**: GET
- **功能说明**: 获取用户的AI分析历史记录
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| analysisType | String | 否 | 分析类型筛选 |

- **响应参数**:

分页格式的分析历史记录列表

## 11. 标签模块

### 11.1 获取所有标签

- **接口URL**: `/v1/tags`
- **请求方式**: GET
- **功能说明**: 获取系统中所有标签
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| type | String | 否 | 标签类型筛选 |
| status | String | 否 | 标签状态筛选（active/inactive） |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| tagId | String | 标签ID |
| name | String | 标签名称 |
| description | String | 标签描述 |
| type | String | 标签类型 |
| color | String | 标签颜色 |
| icon | String | 标签图标 |
| usageCount | Integer | 使用次数 |
| status | String | 标签状态 |
| createTime | String | 创建时间 |

### 11.2 获取热门标签

- **接口URL**: `/v1/tags/hot`
- **请求方式**: GET
- **功能说明**: 获取热门标签列表
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| limit | Integer | 否 | 返回数量限制，默认20 |
| type | String | 否 | 标签类型筛选 |

- **响应参数**:

热门标签列表，格式同11.1

### 11.3 获取标签详情

- **接口URL**: `/v1/tags/{tagId}`
- **请求方式**: GET
- **功能说明**: 获取标签详细信息
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| tagId | String | 是 | 标签ID（路径参数） |

- **响应参数**:

标签详细信息，格式同11.1，包含更多统计数据

### 11.4 搜索标签

- **接口URL**: `/v1/tags/search`
- **请求方式**: GET
- **功能说明**: 搜索标签
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| keyword | String | 是 | 搜索关键词 |
| type | String | 否 | 标签类型筛选 |
| limit | Integer | 否 | 返回数量限制，默认10 |

- **响应参数**:

匹配的标签列表

### 11.5 获取标签相关内容

- **接口URL**: `/v1/tags/{tagId}/content`
- **请求方式**: GET
- **功能说明**: 获取标签相关的内容（文章和热点话题）
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| tagId | String | 是 | 标签ID（路径参数） |
| contentType | String | 否 | 内容类型（article/topic/all），默认all |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| sortBy | String | 否 | 排序字段，默认publishTime |
| sortOrder | String | 否 | 排序方向，默认desc |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| tagInfo | Object | 标签信息 |
| articles | Object | 相关文章（分页格式） |
| topics | Object | 相关热点话题（分页格式） |
| totalCount | Long | 总内容数量 |
| relatedTags | Array | 相关标签 |

## 12. 系统管理模块

### 12.1 获取系统配置

- **接口URL**: `/v1/system/config`
- **请求方式**: GET
- **功能说明**: 获取系统配置信息
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| appVersion | String | 应用版本 |
| apiVersion | String | API版本 |
| features | Object | 功能开关配置 |
| limits | Object | 系统限制配置 |
| maintenance | Object | 维护模式配置 |

features对象结构：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| aiChatEnabled | Boolean | AI聊天功能是否启用 |
| aiAnalysisEnabled | Boolean | AI分析功能是否启用 |
| searchEnabled | Boolean | 搜索功能是否启用 |
| uploadEnabled | Boolean | 上传功能是否启用 |

limits对象结构：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| maxFileSize | Long | 最大文件大小（字节） |
| maxSearchResults | Integer | 最大搜索结果数 |
| maxChatHistory | Integer | 最大聊天历史记录数 |
| rateLimitPerMinute | Integer | 每分钟请求限制 |

### 12.2 系统健康检查

- **接口URL**: `/v1/system/health`
- **请求方式**: GET
- **功能说明**: 系统健康状态检查
- **认证要求**: 无需认证
- **请求参数**: 无
- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| status | String | 系统状态（healthy/unhealthy） |
| timestamp | String | 检查时间 |
| services | Object | 各服务状态 |
| version | String | 系统版本 |
| uptime | Long | 运行时间（秒） |

services对象结构：

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| database | String | 数据库状态 |
| redis | String | Redis状态 |
| ai | String | AI服务状态 |
| storage | String | 存储服务状态 |

### 12.3 获取系统统计

- **接口URL**: `/v1/system/stats`
- **请求方式**: GET
- **功能说明**: 获取系统统计信息
- **认证要求**: 需要认证（管理员权限）
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| userCount | Long | 用户总数 |
| articleCount | Long | 文章总数 |
| topicCount | Long | 热点话题总数 |
| searchCount | Long | 搜索总次数 |
| aiChatCount | Long | AI聊天总次数 |
| todayActiveUsers | Long | 今日活跃用户数 |
| todayNewUsers | Long | 今日新增用户数 |

## 13. 错误码说明

### 13.1 通用错误码

| 错误码 | 说明 | 解决方案 |
| ------ | ---- | -------- |
| 0 | 成功 | - |
| 1001 | 参数错误 | 检查请求参数格式和必填项 |
| 1002 | 认证失败 | 检查token是否有效 |
| 1003 | 权限不足 | 检查用户权限 |
| 1004 | 资源不存在 | 检查资源ID是否正确 |
| 1005 | 请求频率过高 | 降低请求频率 |
| 1006 | 服务暂不可用 | 稍后重试 |
| 1007 | 数据格式错误 | 检查数据格式 |
| 1008 | 文件上传失败 | 检查文件格式和大小 |
| 1009 | 系统维护中 | 等待维护完成 |
| 1010 | 版本不兼容 | 升级客户端版本 |

### 13.2 业务错误码

| 错误码 | 说明 | 解决方案 |
| ------ | ---- | -------- |
| 2001 | 手机号格式错误 | 检查手机号格式 |
| 2002 | 验证码错误或已过期 | 重新获取验证码 |
| 2003 | 密码格式错误 | 检查密码长度和格式 |
| 2004 | 用户不存在 | 检查用户信息 |
| 2005 | 用户已被禁用 | 联系管理员 |
| 2006 | 登录设备过多 | 退出其他设备后重试 |
| 2007 | 搜索关键词为空 | 输入搜索关键词 |
| 2008 | 搜索结果为空 | 尝试其他关键词 |
| 2009 | AI服务不可用 | 稍后重试 |
| 2010 | 内容不存在或已删除 | 检查内容ID |
| 2011 | 标签不存在 | 检查标签ID |
| 2012 | 文件格式不支持 | 使用支持的文件格式 |
| 2013 | 文件大小超限 | 压缩文件后重试 |
| 2014 | 操作过于频繁 | 稍后重试 |
| 2015 | 内容已收藏 | 无需重复收藏 |
| 2016 | 内容未收藏 | 先收藏后操作 |

### 13.3 系统错误码

| 错误码 | 说明 | 解决方案 |
| ------ | ---- | -------- |
| 5001 | 数据库连接失败 | 检查网络连接 |
| 5002 | Redis连接失败 | 检查网络连接 |
| 5003 | 外部API调用失败 | 稍后重试 |
| 5004 | 文件存储服务异常 | 稍后重试 |
| 5005 | 消息队列异常 | 稍后重试 |
| 5006 | 缓存服务异常 | 稍后重试 |
| 5007 | 定时任务执行失败 | 查看系统日志 |
| 5008 | 数据同步失败 | 稍后重试 |
| 5009 | 配置加载失败 | 检查配置文件 |
| 5010 | 服务内部错误 | 联系技术支持 |

---

## 附录

### A. 数据格式说明

#### A.1 时间格式
所有时间字段均使用ISO 8601格式：`yyyy-MM-dd'T'HH:mm:ss.SSS'Z'`

示例：`2024-01-15T10:30:45.123Z`

#### A.2 分页格式
```json
{
    "content": [],
    "totalElements": 100,
    "totalPages": 10,
    "size": 10,
    "number": 0,
    "first": true,
    "last": false,
    "numberOfElements": 10
}
```

#### A.3 统一响应格式
```json
{
    "code": 0,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-15T10:30:45.123Z"
}
```

### B. 认证说明

#### B.1 JWT Token格式
- Header: `Authorization: Bearer {token}`
- Token有效期：24小时
- RefreshToken有效期：30天

#### B.2 权限级别
- 普通用户：基础功能访问权限
- VIP用户：高级功能访问权限
- 管理员：系统管理权限

### C. 版本更新记录

#### v1.0.0 (2024-01-15)
- 初始版本发布
- 基础用户认证功能
- 文章和热点话题浏览功能
- 基础搜索功能

#### v1.1.0 (2024-02-01)
- 新增AI聊天功能
- 新增AI内容分析功能
- 优化搜索算法
- 新增用户交互功能（点赞、收藏）

#### v1.2.0 (2024-02-15)
- 新增标签系统
- 优化分页性能
- 新增系统管理接口
- 完善错误处理机制
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| topicId | String | 是 | 话题ID(路径参数) |

- **响应参数**: 无

### 7.5 获取热点话题标签

- **接口URL**: `/v1/hot-topics/{topicId}/tags`
- **请求方式**: GET
- **功能说明**: 获取热点话题的标签列表
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| topicId | String | 是 | 话题ID(路径参数) |

- **响应参数**:

标签列表数组

### 7.6 手动刷新热点话题

- **接口URL**: `/v1/hot-topics/refresh`
- **请求方式**: POST
- **功能说明**: 手动触发热点话题更新
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.7 手动刷新昨日热点话题

- **接口URL**: `/v1/hot-topics/refresh-yesterday`
- **请求方式**: POST
- **功能说明**: 手动触发昨日热点话题更新
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.8 刷新热点话题排名

- **接口URL**: `/v1/hot-topics/refresh-ranks`
- **请求方式**: POST
- **功能说明**: 手动刷新热点话题排名
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.9 刷新热点话题趋势

- **接口URL**: `/v1/hot-topics/refresh-trends`
- **请求方式**: POST
- **功能说明**: 手动刷新热点话题趋势
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

### 7.10 刷新昨日热点话题趋势

- **接口URL**: `/v1/hot-topics/refresh-yesterday-trends`
- **请求方式**: POST
- **功能说明**: 手动刷新昨日热点话题趋势
- **认证要求**: 需要认证
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

## 8. 用户交互模块

### 8.1 收藏内容

- **接口URL**: `/v1/favorites`
- **请求方式**: POST
- **功能说明**: 收藏内容
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| favoriteId | String | 收藏ID |

### 8.2 取消收藏

- **接口URL**: `/v1/favorites/{favoriteId}`
- **请求方式**: DELETE
- **功能说明**: 取消收藏
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| favoriteId | String | 是 | 收藏ID(路径参数) |

- **响应参数**: 无

### 8.3 获取收藏列表

- **接口URL**: `/v1/favorites`
- **请求方式**: GET
- **功能说明**: 获取用户收藏列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentType | String | 否 | 内容类型(article/topic)，不传则获取全部 |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 收藏列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |

### 8.4 点赞内容

- **接口URL**: `/v1/likes`
- **请求方式**: POST
- **功能说明**: 点赞内容
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic/comment) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| likeId | String | 点赞ID |

### 8.5 取消点赞

- **接口URL**: `/v1/likes/{likeId}`
- **请求方式**: DELETE
- **功能说明**: 取消点赞
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| likeId | String | 是 | 点赞ID(路径参数) |

- **响应参数**: 无

### 8.6 发表评论

- **接口URL**: `/v1/comments`
- **请求方式**: POST
- **功能说明**: 发表评论
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic) |
| content | String | 是 | 评论内容 |
| parentId | String | 否 | 父评论ID(回复评论时需要) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| commentId | String | 评论ID |

### 8.7 获取评论列表

- **接口URL**: `/v1/comments`
- **请求方式**: GET
- **功能说明**: 获取内容的评论列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic) |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 评论列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |

## 9. 系统通知模块

### 9.1 获取系统通知列表

- **接口URL**: `/v1/notices`
- **请求方式**: GET
- **功能说明**: 获取系统通知列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 通知列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| unreadCount | Integer | 未读通知数量 |

### 9.2 获取通知详情

- **接口URL**: `/v1/notices/{noticeId}`
- **请求方式**: GET
- **功能说明**: 获取通知详情
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| noticeId | String | 是 | 通知ID(路径参数) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| noticeId | String | 通知ID |
| title | String | 通知标题 |
| content | String | 通知内容 |
| noticeType | Integer | 通知类型 |
| imageUrl | String | 图片URL |
| publishTime | String | 发布时间 |
| isRead | Boolean | 是否已读 |

### 9.3 标记通知已读

- **接口URL**: `/v1/notices/read`
- **请求方式**: PUT
- **功能说明**: 标记通知为已读
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| noticeIds | Array | 是 | 通知ID列表 |

- **响应参数**: 无

## 10. 错误码说明

| 错误码 | 错误信息 | 说明 |
| ------ | -------- | ---- |
| 0 | success | 成功 |
| 1000 | System error | 系统错误 |
| 2000 | Invalid parameter | 参数不合法 |
| 3000 | Unauthorized | 未授权 |
| 3001 | Token expired | 令牌过期 |
| 3002 | Invalid token | 无效令牌 |
| 4000 | Resource not found | 资源不存在 |
| 5000 | Business error | 业务错误 |
| 5001 | Phone already registered | 手机号已注册 |
| 5002 | Invalid verification code | 验证码错误 |
| 5003 | Verification code expired | 验证码过期 |
| 5004 | Password error | 密码错误 